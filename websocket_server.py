import asyncio
import websockets
import json
import torch
import numpy as np
import os
import sys
from models.llama_model import LLaMAHF, LLaMAHFConfig
import models.tae as tae
import options.option_transformer as option_trans
from sentence_transformers import SentenceTransformer
from visualization.recover_visualize import recover_from_local_position
import warnings
warnings.filterwarnings('ignore')

class MotionStreamerServer:
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.setup_models()
        
    def setup_models(self):
        """初始化模型"""
        print("正在初始化模型...")
        
        # 获取参数
        args = option_trans.get_args_parser()
        torch.manual_seed(args.seed)
        
        # 加载T5模型
        self.t5_model = SentenceTransformer('sentencet5-xxl/')
        self.t5_model.eval()
        for p in self.t5_model.parameters():
            p.requires_grad = False
            
        # 设置网络参数
        clip_range = [-30, 20]
        
        # 初始化TAE网络
        self.net = tae.Causal_HumanTAE(
            hidden_size=args.hidden_size,
            down_t=args.down_t,
            stride_t=args.stride_t,
            depth=args.depth,
            dilation_growth_rate=args.dilation_growth_rate,
            activation='relu',
            latent_dim=args.latent_dim,
            clip_range=clip_range
        )
        
        # 初始化Transformer
        config = LLaMAHFConfig.from_name('Normal_size')
        config.block_size = 78
        self.trans_encoder = LLaMAHF(config, args.num_diffusion_head_layers, args.latent_dim, self.device)
        
        # 加载检查点
        print(f'正在加载检查点: {args.resume_pth}')
        ckpt = torch.load(args.resume_pth, map_location='cpu')
        self.net.load_state_dict(ckpt['net'], strict=True)
        self.net.eval()
        self.net.to(self.device)
        
        if args.resume_trans is not None:
            print(f'正在加载transformer检查点: {args.resume_trans}')
            ckpt = torch.load(args.resume_trans, map_location='cpu')
            new_ckpt_trans = {}
            for key in ckpt['trans'].keys():
                if key.split('.')[0] == 'module':
                    new_key = '.'.join(key.split('.')[1:])
                else:
                    new_key = key
                new_ckpt_trans[new_key] = ckpt['trans'][key]
            self.trans_encoder.load_state_dict(new_ckpt_trans, strict=True)
        
        self.trans_encoder.eval()
        self.trans_encoder.to(self.device)
        
        # 加载参考数据
        self.reference_end_latent = np.load('reference_end_latent_t2m_272.npy')
        self.reference_end_latent = torch.from_numpy(self.reference_end_latent).to(self.device)
        
        # 加载均值和标准差
        self.mean = np.load('humanml3d_272/mean_std/Mean.npy')
        self.std = np.load('humanml3d_272/mean_std/Std.npy')
        
        print("模型初始化完成!")
        
    def generate_motion(self, text, mode='pos'):
        """根据文本生成动作"""
        try:
            print(f"正在生成动作: {text}")
            
            # 生成动作潜在表示
            threshold = 0.1
            motion_latents = self.trans_encoder.sample_for_eval_CFG_inference(
                text=text, 
                tokenizer=self.t5_model, 
                device=self.device, 
                reference_end_latent=self.reference_end_latent, 
                threshold=threshold
            )
            
            # 解码动作序列
            motion_seqs = self.net.forward_decoder(motion_latents)
            motion = motion_seqs.squeeze(0)
            motion = motion.detach().cpu().numpy()
            
            if mode == 'pos':
                # 恢复关节位置
                pred_xyz = recover_from_local_position(motion * self.std + self.mean, 22)
                # 返回形状: (frames, 22, 3)
                return pred_xyz.tolist()
            else:
                # 返回原始272维表示
                return motion.tolist()
                
        except Exception as e:
            print(f"生成动作时出错: {e}")
            return None
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        print(f"客户端已连接: {websocket.remote_address}")
        
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    command = data.get('command')
                    
                    if command == 'generate_motion':
                        text = data.get('text', '')
                        mode = data.get('mode', 'pos')
                        
                        if not text:
                            await websocket.send(json.dumps({
                                'status': 'error',
                                'message': '文本不能为空'
                            }))
                            continue
                        
                        # 生成动作
                        motion_data = self.generate_motion(text, mode)
                        
                        if motion_data is not None:
                            response = {
                                'status': 'success',
                                'text': text,
                                'mode': mode,
                                'motion_data': motion_data,
                                'frames': len(motion_data),
                                'joints': 22 if mode == 'pos' else 272
                            }
                        else:
                            response = {
                                'status': 'error',
                                'message': '动作生成失败'
                            }
                        
                        await websocket.send(json.dumps(response))
                        
                    elif command == 'ping':
                        await websocket.send(json.dumps({'status': 'pong'}))
                        
                    else:
                        await websocket.send(json.dumps({
                            'status': 'error',
                            'message': f'未知命令: {command}'
                        }))
                        
                except json.JSONDecodeError:
                    await websocket.send(json.dumps({
                        'status': 'error',
                        'message': '无效的JSON格式'
                    }))
                except Exception as e:
                    print(f"处理消息时出错: {e}")
                    await websocket.send(json.dumps({
                        'status': 'error',
                        'message': str(e)
                    }))
                    
        except websockets.exceptions.ConnectionClosed:
            print(f"客户端断开连接: {websocket.remote_address}")
        except Exception as e:
            print(f"连接处理出错: {e}")

    async def start_server(self, host='localhost', port=8765):
        """启动WebSocket服务器"""
        print(f"正在启动WebSocket服务器 {host}:{port}")
        
        async with websockets.serve(self.handle_client, host, port):
            print(f"MotionStreamer WebSocket服务器已启动在 ws://{host}:{port}")
            print("等待客户端连接...")
            await asyncio.Future()  # 保持服务器运行

if __name__ == "__main__":
    server = MotionStreamerServer()
    
    # 启动服务器
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        print("\n服务器已停止")

bl_info = {
    "name": "MotionStreamer MMD Driver",
    "author": "MotionStreamer Team",
    "version": (1, 0, 0),
    "blender": (3, 0, 0),
    "location": "View3D > Sidebar > MotionStreamer",
    "description": "通过WebSocket连接MotionStreamer，实现文本到MMD动作的实时生成",
    "category": "Animation",
}

import bpy
import bmesh
import mathutils
import asyncio
import websockets
import json
import threading
import time
from bpy.props import StringProperty, BoolProperty, EnumProperty, IntProperty, FloatProperty
from bpy.types import Panel, Operator, PropertyGroup

# 全局变量
motion_data_cache = []
is_playing = False
current_frame = 0
websocket_connection = None
connection_status = "未连接"

class MotionStreamerProperties(PropertyGroup):
    """MotionStreamer属性组"""
    
    # WebSocket连接设置
    server_host: StringProperty(
        name="服务器地址",
        description="MotionStreamer服务器地址",
        default="localhost"
    )
    
    server_port: IntProperty(
        name="端口",
        description="MotionStreamer服务器端口",
        default=8765,
        min=1,
        max=65535
    )
    
    # 文本输入
    motion_text: StringProperty(
        name="动作描述",
        description="输入要生成的动作描述文本",
        default="A person is walking forward"
    )
    
    # 动作模式
    motion_mode: EnumProperty(
        name="动作模式",
        description="选择动作数据模式",
        items=[
            ('pos', '位置模式', '使用关节位置数据'),
            ('rot', '旋转模式', '使用关节旋转数据')
        ],
        default='pos'
    )
    
    # MMD模型选择
    target_armature: StringProperty(
        name="目标骨架",
        description="选择要驱动的MMD模型骨架",
        default=""
    )
    
    # 播放设置
    auto_play: BoolProperty(
        name="自动播放",
        description="生成动作后自动播放",
        default=True
    )
    
    playback_speed: FloatProperty(
        name="播放速度",
        description="动作播放速度倍数",
        default=1.0,
        min=0.1,
        max=5.0
    )
    
    loop_animation: BoolProperty(
        name="循环播放",
        description="循环播放生成的动作",
        default=True
    )

class MOTIONSTREAMER_OT_connect(Operator):
    """连接到MotionStreamer服务器"""
    bl_idname = "motionstreamer.connect"
    bl_label = "连接服务器"
    bl_description = "连接到MotionStreamer WebSocket服务器"
    
    def execute(self, context):
        props = context.scene.motionstreamer_props
        
        def connect_websocket():
            global websocket_connection, connection_status
            
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                async def connect():
                    global websocket_connection, connection_status
                    uri = f"ws://{props.server_host}:{props.server_port}"
                    
                    try:
                        websocket_connection = await websockets.connect(uri)
                        connection_status = "已连接"
                        print(f"已连接到MotionStreamer服务器: {uri}")
                        
                        # 发送ping测试连接
                        await websocket_connection.send(json.dumps({"command": "ping"}))
                        response = await websocket_connection.recv()
                        data = json.loads(response)
                        
                        if data.get('status') == 'pong':
                            print("服务器连接测试成功")
                        
                    except Exception as e:
                        connection_status = f"连接失败: {str(e)}"
                        print(f"连接失败: {e}")
                
                loop.run_until_complete(connect())
                
            except Exception as e:
                connection_status = f"连接错误: {str(e)}"
                print(f"连接错误: {e}")
        
        # 在新线程中连接
        thread = threading.Thread(target=connect_websocket)
        thread.daemon = True
        thread.start()
        
        self.report({'INFO'}, "正在连接服务器...")
        return {'FINISHED'}

class MOTIONSTREAMER_OT_disconnect(Operator):
    """断开服务器连接"""
    bl_idname = "motionstreamer.disconnect"
    bl_label = "断开连接"
    bl_description = "断开与MotionStreamer服务器的连接"
    
    def execute(self, context):
        global websocket_connection, connection_status
        
        if websocket_connection:
            try:
                asyncio.run(websocket_connection.close())
                websocket_connection = None
                connection_status = "未连接"
                self.report({'INFO'}, "已断开服务器连接")
            except Exception as e:
                self.report({'ERROR'}, f"断开连接时出错: {e}")
        else:
            self.report({'INFO'}, "未连接到服务器")
        
        return {'FINISHED'}

class MOTIONSTREAMER_OT_generate_motion(Operator):
    """生成动作"""
    bl_idname = "motionstreamer.generate_motion"
    bl_label = "生成动作"
    bl_description = "根据文本描述生成动作"
    
    def execute(self, context):
        global websocket_connection, motion_data_cache
        
        if not websocket_connection:
            self.report({'ERROR'}, "请先连接到服务器")
            return {'CANCELLED'}
        
        props = context.scene.motionstreamer_props
        
        if not props.motion_text.strip():
            self.report({'ERROR'}, "请输入动作描述文本")
            return {'CANCELLED'}
        
        def generate_motion():
            global motion_data_cache
            
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                async def send_request():
                    global motion_data_cache
                    
                    request = {
                        "command": "generate_motion",
                        "text": props.motion_text,
                        "mode": props.motion_mode
                    }
                    
                    await websocket_connection.send(json.dumps(request))
                    response = await websocket_connection.recv()
                    data = json.loads(response)
                    
                    if data.get('status') == 'success':
                        motion_data_cache = data.get('motion_data', [])
                        print(f"动作生成成功: {len(motion_data_cache)} 帧")
                        
                        # 在主线程中应用动作
                        bpy.app.timers.register(lambda: apply_motion_to_armature(context))
                        
                    else:
                        print(f"动作生成失败: {data.get('message', '未知错误')}")
                
                loop.run_until_complete(send_request())
                
            except Exception as e:
                print(f"生成动作时出错: {e}")
        
        # 在新线程中生成动作
        thread = threading.Thread(target=generate_motion)
        thread.daemon = True
        thread.start()
        
        self.report({'INFO'}, f"正在生成动作: {props.motion_text}")
        return {'FINISHED'}

def apply_motion_to_armature(context):
    """将动作应用到骨架"""
    global motion_data_cache
    
    if not motion_data_cache:
        return None
    
    props = context.scene.motionstreamer_props
    
    # 查找目标骨架
    armature = None
    if props.target_armature:
        armature = bpy.data.objects.get(props.target_armature)
    else:
        # 自动查找第一个骨架对象
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE':
                armature = obj
                break
    
    if not armature:
        print("未找到目标骨架")
        return None
    
    # 应用动作数据
    try:
        apply_motion_data_to_armature(armature, motion_data_cache, props.motion_mode)
        
        if props.auto_play:
            bpy.ops.screen.animation_play()
        
        print(f"动作已应用到骨架: {armature.name}")
        
    except Exception as e:
        print(f"应用动作时出错: {e}")
    
    return None

def apply_motion_data_to_armature(armature, motion_data, mode):
    """将动作数据应用到骨架对象"""
    if not motion_data or not armature:
        return
    
    # 设置动画帧范围
    frame_count = len(motion_data)
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = frame_count
    
    # 选择并激活骨架
    bpy.context.view_layer.objects.active = armature
    bpy.ops.object.mode_set(mode='POSE')
    
    # 清除现有动画数据
    if armature.animation_data:
        armature.animation_data_clear()
    
    # 创建新的动作
    action = bpy.data.actions.new(name=f"MotionStreamer_{int(time.time())}")
    armature.animation_data_create()
    armature.animation_data.action = action
    
    if mode == 'pos':
        # 位置模式：应用关节位置数据
        apply_position_data(armature, motion_data)
    else:
        # 旋转模式：应用272维数据
        apply_rotation_data(armature, motion_data)

def apply_position_data(armature, motion_data):
    """应用位置数据到骨架"""
    # HumanML3D的22个关节映射到MMD骨骼
    joint_mapping = {
        0: "センター",      # pelvis
        1: "上半身",        # left_hip
        2: "上半身",        # right_hip
        3: "上半身2",       # spine1
        4: "首",           # spine2
        5: "頭",           # spine3
        6: "頭",           # neck
        7: "頭",           # head
        8: "左肩",         # left_collar
        9: "左腕",         # left_shoulder
        10: "左ひじ",      # left_elbow
        11: "左手首",      # left_wrist
        12: "右肩",        # right_collar
        13: "右腕",        # right_shoulder
        14: "右ひじ",      # right_elbow
        15: "右手首",      # right_wrist
        16: "左足",        # left_hip
        17: "左ひざ",      # left_knee
        18: "左足首",      # left_ankle
        19: "右足",        # right_hip
        20: "右ひざ",      # right_knee
        21: "右足首",      # right_ankle
    }
    
    for frame_idx, frame_data in enumerate(motion_data):
        bpy.context.scene.frame_set(frame_idx + 1)
        
        for joint_idx, position in enumerate(frame_data):
            if joint_idx < len(joint_mapping):
                bone_name = joint_mapping.get(joint_idx)
                if bone_name and bone_name in armature.pose.bones:
                    bone = armature.pose.bones[bone_name]
                    
                    # 设置骨骼位置
                    bone.location = mathutils.Vector(position)
                    bone.keyframe_insert(data_path="location")

def apply_rotation_data(armature, motion_data):
    """应用旋转数据到骨架（272维数据）"""
    # 这里需要根据272维表示的具体格式来解析
    # 暂时使用简化的处理方式
    for frame_idx, frame_data in enumerate(motion_data):
        bpy.context.scene.frame_set(frame_idx + 1)

        # 简化处理：只处理根骨骼的移动
        if "センター" in armature.pose.bones:
            root_bone = armature.pose.bones["センター"]

            # 从272维数据中提取根骨骼的位置信息
            if len(frame_data) >= 2:
                root_bone.location.x = frame_data[0] * 0.1  # 缩放因子
                root_bone.location.z = frame_data[1] * 0.1
                root_bone.keyframe_insert(data_path="location")

class MOTIONSTREAMER_OT_play_animation(Operator):
    """播放动画"""
    bl_idname = "motionstreamer.play_animation"
    bl_label = "播放动画"
    bl_description = "播放生成的动作动画"

    def execute(self, context):
        global motion_data_cache

        if not motion_data_cache:
            self.report({'ERROR'}, "没有可播放的动作数据")
            return {'CANCELLED'}

        bpy.ops.screen.animation_play()
        self.report({'INFO'}, "开始播放动画")
        return {'FINISHED'}

class MOTIONSTREAMER_OT_stop_animation(Operator):
    """停止动画"""
    bl_idname = "motionstreamer.stop_animation"
    bl_label = "停止动画"
    bl_description = "停止播放动画"

    def execute(self, context):
        bpy.ops.screen.animation_cancel()
        self.report({'INFO'}, "动画已停止")
        return {'FINISHED'}

class MOTIONSTREAMER_PT_main_panel(Panel):
    """MotionStreamer主面板"""
    bl_label = "MotionStreamer"
    bl_idname = "MOTIONSTREAMER_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "MotionStreamer"

    def draw(self, context):
        layout = self.layout
        props = context.scene.motionstreamer_props

        # 连接状态显示
        box = layout.box()
        box.label(text="服务器连接", icon='NETWORK_DRIVE')

        row = box.row()
        row.prop(props, "server_host")
        row.prop(props, "server_port")

        row = box.row()
        if websocket_connection:
            row.operator("motionstreamer.disconnect", icon='CANCEL')
            row.label(text=connection_status, icon='CHECKMARK')
        else:
            row.operator("motionstreamer.connect", icon='PLAY')
            row.label(text=connection_status, icon='ERROR')

class MOTIONSTREAMER_PT_motion_panel(Panel):
    """动作生成面板"""
    bl_label = "动作生成"
    bl_idname = "MOTIONSTREAMER_PT_motion_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "MotionStreamer"
    bl_parent_id = "MOTIONSTREAMER_PT_main_panel"

    def draw(self, context):
        layout = self.layout
        props = context.scene.motionstreamer_props

        # 文本输入
        box = layout.box()
        box.label(text="动作描述", icon='TEXT')
        box.prop(props, "motion_text", text="")
        box.prop(props, "motion_mode")

        # 生成按钮
        row = box.row()
        row.scale_y = 1.5
        row.operator("motionstreamer.generate_motion", icon='PLAY')

        # 显示动作数据信息
        if motion_data_cache:
            info_box = layout.box()
            info_box.label(text="动作信息", icon='INFO')
            info_box.label(text=f"帧数: {len(motion_data_cache)}")
            if motion_data_cache:
                info_box.label(text=f"关节数: {len(motion_data_cache[0])}")

class MOTIONSTREAMER_PT_control_panel(Panel):
    """播放控制面板"""
    bl_label = "播放控制"
    bl_idname = "MOTIONSTREAMER_PT_control_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "MotionStreamer"
    bl_parent_id = "MOTIONSTREAMER_PT_main_panel"

    def draw(self, context):
        layout = self.layout
        props = context.scene.motionstreamer_props

        # 目标设置
        box = layout.box()
        box.label(text="目标设置", icon='ARMATURE_DATA')
        box.prop_search(props, "target_armature", bpy.data, "objects")

        # 播放设置
        box = layout.box()
        box.label(text="播放设置", icon='SETTINGS')
        box.prop(props, "auto_play")
        box.prop(props, "playback_speed")
        box.prop(props, "loop_animation")

        # 播放控制按钮
        row = box.row()
        row.operator("motionstreamer.play_animation", icon='PLAY')
        row.operator("motionstreamer.stop_animation", icon='PAUSE')

# 注册类列表
classes = [
    MotionStreamerProperties,
    MOTIONSTREAMER_OT_connect,
    MOTIONSTREAMER_OT_disconnect,
    MOTIONSTREAMER_OT_generate_motion,
    MOTIONSTREAMER_OT_play_animation,
    MOTIONSTREAMER_OT_stop_animation,
    MOTIONSTREAMER_PT_main_panel,
    MOTIONSTREAMER_PT_motion_panel,
    MOTIONSTREAMER_PT_control_panel,
]

def register():
    """注册插件"""
    for cls in classes:
        bpy.utils.register_class(cls)
    
    bpy.types.Scene.motionstreamer_props = bpy.props.PointerProperty(type=MotionStreamerProperties)

def unregister():
    """注销插件"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
    
    del bpy.types.Scene.motionstreamer_props

if __name__ == "__main__":
    register()
